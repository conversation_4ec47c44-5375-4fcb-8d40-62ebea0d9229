{"name": "cm-panel-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.465.0", "@fastify/multipart": "^8.2.0", "@google-cloud/storage": "^7.7.0", "@nestjs/common": "^10.1.3", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.3.1", "@nestjs/jwt": "^10.1.0", "@nestjs/platform-fastify": "10.1.3", "@nestjs/websockets": "^10.3.3", "@prisma/client": "^5.2.0", "aws-sdk": "^2.1510.0", "axios": "^1.6.8", "busboy": "^1.6.0", "cryptr": "^6.3.0", "dayjs": "^1.11.9", "fastify-multipart": "^5.4.0", "nestjs-zod": "^3.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "slugify": "^1.6.6"}, "devDependencies": {"@nestjs/cli": "^10.1.12", "@nestjs/schematics": "^10.0.2", "@nestjs/testing": "^10.1.3", "@types/busboy": "^1.5.3", "@types/express": "^4.17.13", "@types/fastify-multipart": "^0.7.0", "@types/jest": "28.1.8", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.3", "prettier": "^2.3.2", "prisma": "^5.2.0", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}