generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model category {
  id          Int       @id @default(autoincrement())
  name        String    @db.VarChar(50)
  description String?   @db.VarChar(255)
  hidden      Boolean   @default(false)
  order       Int?      @default(1)
  deleted_at  DateTime? @db.DateTime(0)
  course      course[]
}

model code {
  id          Int       @id @unique(map: "id") @default(autoincrement())
  code        String    @db.VarChar(100)
  type        String    @db.VarChar(50)
  created_at  DateTime? @db.DateTime(0)
  updated_at  DateTime? @db.DateTime(0)
  verified_at DateTime? @db.DateTime(0)
  deleted_at  DateTime? @db.DateTime(0)
  user_id     Int
  user        user      @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK__user")

  @@index([user_id], map: "FK__user")
}

model comment {
  id         Int      @id @default(autoincrement())
  content    String   @db.Var<PERSON>har(255)
  created_at DateTime @db.DateTime(0)
  updated_at DateTime @db.DateTime(0)
  user_id    Int      @default(0)
  post_id    Int      @default(0)
  post       post     @relation(fields: [post_id], references: [id], onDelete: NoAction, onUpdate: Restrict, map: "FK_comments_post")
  user       user     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: Restrict, map: "FK_comments_user")

  @@index([post_id], map: "FK_comments_post")
  @@index([user_id], map: "FK_comments_user")
}

model config {
  id    Int     @id @default(autoincrement())
  key   String? @db.Text
  value String? @db.VarChar(50)
}

model course {
  id          Int           @id @default(autoincrement())
  name        String        @db.VarChar(50)
  description String?       @db.VarChar(255)
  category_id Int?
  hidden      Boolean       @default(false)
  order       Int?          @default(1)
  deleted_at  DateTime?     @db.DateTime(0)
  category    category?     @relation(fields: [category_id], references: [id], onUpdate: NoAction, map: "FK_course_category_id")
  level       level[]
  user_course user_course[]

  @@index([category_id], map: "FK_course_category_id")
}

model credential {
  id                                  Int     @id @default(autoincrement())
  name                                String? @db.VarChar(50)
  surname                             String? @db.VarChar(255)
  phone                               String? @db.VarChar(255)
  sex                                 String? @db.VarChar(255)
  user_id                             Int?
  user_credential_user_idTouser       user?   @relation("credential_user_idTouser", fields: [user_id], references: [id], onUpdate: Restrict, map: "FK_credential_user")
  user_credentialTouser_credential_id user[]  @relation("credentialTouser_credential_id")

  @@index([user_id], map: "FK_credential_user")
}

model feed {
  id         Int       @id @default(autoincrement())
  type       String    @db.VarChar(50)
  user_id    Int?
  content    String    @db.VarChar(150)
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
  deleted_at DateTime? @db.DateTime(0)
  has_read   Boolean?  @default(false)
  user       user?     @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "FK_feed_user")

  @@index([user_id], map: "FK_feed_user")
}

model file {
  id         Int           @id @default(autoincrement())
  name       String?       @db.VarChar(50)
  extension  String?       @db.VarChar(255)
  cover      String?       @db.VarChar(255)
  provider   file_provider @default(GOOGLE)
  filesize   Int?
  path       String?       @db.VarChar(500)
  created_at DateTime?     @db.DateTime(0)
  updated_at DateTime?     @db.DateTime(0)
  level_id   Int?
  hidden     Int           @default(0)
  order      Int?          @default(1)
  deleted_at DateTime?     @db.DateTime(0)
  level      level?        @relation(fields: [level_id], references: [id], onUpdate: NoAction, map: "FK_file_level_id")

  @@index([level_id], map: "FK_file_level_id")
}

model level {
  id         Int       @id @default(autoincrement())
  title      String?   @db.VarChar(255)
  course_id  Int?
  hidden     Int       @default(0) @db.TinyInt
  order      Int?      @default(1)
  deleted_at DateTime? @db.DateTime(0)
  file       file[]
  course     course?   @relation(fields: [course_id], references: [id], onUpdate: NoAction, map: "FK_level_course_id")

  @@index([course_id], map: "FK_level_course_id")
}

model like {
  id         Int      @id @default(autoincrement())
  created_at DateTime @db.DateTime(0)
  updated_at DateTime @db.DateTime(0)
  user_id    Int      @default(0)
  post_id    Int      @default(0)
  post       post     @relation(fields: [post_id], references: [id], onDelete: NoAction, onUpdate: Restrict, map: "FK_like_post")
  user       user     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: Restrict, map: "FK_like_user")

  @@index([post_id], map: "FK_like_post")
  @@index([user_id], map: "FK_like_user")
}

model message {
  id                      Int          @id @default(autoincrement())
  type                    String       @db.VarChar(50)
  content                 String       @db.VarChar(500)
  from                    Int?
  to                      Int?
  created_at              DateTime?    @db.DateTime(0)
  updated_at              DateTime?    @db.DateTime(0)
  deleted_at              DateTime?    @db.DateTime(0)
  user_course_id          Int?
  has_read                Boolean?     @default(false)
  user_message_fromTouser user?        @relation("message_fromTouser", fields: [from], references: [id], onDelete: NoAction, onUpdate: Restrict, map: "FK_message_from")
  user_message_toTouser   user?        @relation("message_toTouser", fields: [to], references: [id], onDelete: NoAction, onUpdate: Restrict, map: "FK_message_to")
  user_course             user_course? @relation(fields: [user_course_id], references: [id], onDelete: NoAction, onUpdate: Restrict, map: "FK_message_user_course")

  @@index([from], map: "FK_message_from")
  @@index([to], map: "FK_message_to")
  @@index([user_course_id], map: "FK_message_user_course")
}

model post {
  id         Int       @id @default(autoincrement())
  content    String?   @db.VarChar(255)
  image_path String?   @db.VarChar(150)
  post_type  String?   @db.VarChar(50)
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
  user_id    Int       @default(0)
  is_pinned  String?   @default("NOT_PINNED") @db.VarChar(11)
  comment    comment[]
  like       like[]
  user       user      @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "FK_post_user")

  @@index([user_id], map: "FK_post_user")
}

model token {
  id         Int       @id @default(autoincrement())
  hash       String    @db.VarChar(255)
  created_at DateTime  @db.DateTime(0)
  updated_at DateTime  @db.DateTime(0)
  expires_at DateTime  @db.DateTime(0)
  deleted_at DateTime? @db.DateTime(0)
  user_id    Int
  user       user      @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "FK_token_user")

  @@index([user_id], map: "FK_token_user_id")
}

model user {
  id                                  Int           @id @default(autoincrement())
  username                            String        @unique(map: "username") @db.VarChar(255)
  password                            String        @db.VarChar(255)
  email                               String        @unique(map: "email") @db.VarChar(50)
  firebase_id                         String?       @db.VarChar(500)
  user_type                           String        @db.VarChar(255)
  user_level_string                   String?       @default("STUDENT") @db.VarChar(100)
  status                              String        @db.VarChar(255)
  device_id                           String        @db.VarChar(255)
  image_path                          String?       @db.VarChar(255)
  credential_id                       Int?
  utoken                              String?       @db.VarChar(250)
  created_at                          DateTime?     @db.DateTime(0)
  updated_at                          DateTime?     @db.DateTime(0)
  salt                                String?       @db.VarChar(50)
  is_deleted                          Boolean       @default(false)
  code                                code[]
  comment                             comment[]
  credential_credential_user_idTouser credential[]  @relation("credential_user_idTouser")
  feed                                feed[]
  like                                like[]
  message_message_fromTouser          message[]     @relation("message_fromTouser")
  message_message_toTouser            message[]     @relation("message_toTouser")
  post                                post[]
  token                               token[]
  credential                          credential?   @relation("credentialTouser_credential_id", fields: [credential_id], references: [id], onUpdate: Restrict, map: "FK_user_credential_id2")
  educating_courses                   user_course[] @relation("userTouser_course_educator_id")
  learning_courses                    user_course[] @relation("userTouser_course_user_id")

  @@index([credential_id], map: "FK_user_credential_id")
}

model user_course {
  id                Int       @id @default(autoincrement())
  level             Int?
  expires_at        DateTime? @db.DateTime(0)
  created_at        DateTime? @db.DateTime(0)
  updated_at        DateTime? @db.DateTime(0)
  deleted_at        DateTime? @db.DateTime(0)
  course_id         Int?
  user_id           Int?
  educator_id       Int?
  educator_has_read Boolean   @default(false)
  user_has_read     Boolean   @default(false)
  message           message[]
  course            course?   @relation(fields: [course_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_user_course_course")
  educator          user?     @relation("userTouser_course_educator_id", fields: [educator_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_user_course_user")
  user              user?     @relation("userTouser_course_user_id", fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_user_course_user_id")

  @@index([course_id], map: "FK_user_course_course")
  @@index([educator_id], map: "FK_user_course_user")
  @@index([user_id], map: "FK_user_course_user_id")
}

model o_order {
  id            Int       @id @unique(map: "id") @default(autoincrement())
  product_id    Int?
  user_id       Int?
  status        String    @default("CREATED") @db.VarChar(50)
  code          String    @db.VarChar(65)
  price         Int?
  currency_code String?   @default("TL") @db.VarChar(50)
  success_date  DateTime? @db.DateTime(0)
  recurring     Int?      @default(0)
  next_purchase DateTime? @db.DateTime(0)
  error_code    String?   @db.VarChar(50)
  error_reason  String?   @db.VarChar(500)
  error_date    DateTime? @db.DateTime(0)
  created_at    DateTime? @db.DateTime(0)
  updated_at    DateTime? @db.DateTime(0)
  deleted_at    DateTime? @db.DateTime(0)
}

model o_product {
  id                 Int       @id @unique(map: "id") @default(autoincrement())
  type               Int?      @default(1)
  ext_id             Int?      @default(1)
  variant            Int?      @default(1)
  name               String?   @db.VarChar(50)
  description        String?   @db.VarChar(255)
  price              Float?    @db.Float
  undiscounted_price Float?    @db.Float
  is_discounted      Int?      @db.TinyInt
  recurring          Int?      @default(0)
  days               Int?      @default(30)
  currency_code      String?   @default("TL") @db.VarChar(50)
  thumbnail          String?   @db.VarChar(250)
  created_at         DateTime? @db.DateTime(0)
  deleted_at         DateTime? @db.DateTime(0)
}

enum file_provider {
  GOOGLE
  SITE
  EXTERNAL
}
