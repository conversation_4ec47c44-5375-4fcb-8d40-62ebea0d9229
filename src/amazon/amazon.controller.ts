import { Controller, Get, Injectable } from '@nestjs/common';
import { S3Service } from '../services/s3.service';

@Controller('amazon')
export class AmazonController {
  constructor(private readonly s3Service: S3Service) {}

  // @Get('/buckets')
  // getBuckets(){
  //
  // 	return this.s3Service.listAllBuckets()
  //
  // }
  //
  // @Get('/files')
  // getFiles(){
  //
  // 	return this.s3Service.listAllFiles()
  //
  // }
}
