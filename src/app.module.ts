import { JwtModule } from '@nestjs/jwt';
import { APP_PIPE } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { ZodValidationPipe } from 'nestjs-zod';
import { Module, ValidationPipe } from '@nestjs/common';

import { AppService } from './app/app.service';
import { UserService } from './user/user.service';

import { PrismaService } from './services/prisma.service';

import { AuthController } from './auth/auth.controller';
import { AppController } from './app/app.controller';
import { UserController } from './user/user.controller';
import { CoursesController } from './courses/courses.controller';
import { CategoriesController } from './categories/categories.controller';
import { LevelsController } from './levels/levels.controller';
import { GoogleController } from './integrations/google/google.controller';
import { GoogleService } from './services/google.service';
import { FilesController } from './files/files.controller';
import { PaymentController } from './payment/payment.controller';
import { PaymentService } from './payment/payment.service';
import { WebhookController } from './payment/webhook.controller';

@Module({
  imports: [
    ConfigModule.forRoot(),
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET,
    }),
  ],
  controllers: [
    AppController,
    UserController,
    AuthController,
    CoursesController,
    CategoriesController,
    LevelsController,
    GoogleController,
    FilesController,
    PaymentController,
    WebhookController,
  ],
  providers: [
    AppService,
    UserService,
    GoogleService,
    // S3Service,
    PaymentService,
    PrismaService,
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
  ],
})
export class AppModule {}
