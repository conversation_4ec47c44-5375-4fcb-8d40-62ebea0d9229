import { Body, Controller, Get, Post } from '@nestjs/common';
import { AppService } from './app.service';
import { DeleteUserDto } from './dtos/DeleteUserDto';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Post('/delete-user')
  DeleteUser(@Body() body:DeleteUserDto): Promise<{success:true}> {
    return this.appService.DeleteUser(body.username,body.password);
  }
}
