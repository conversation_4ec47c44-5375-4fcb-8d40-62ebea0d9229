import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../services/prisma.service';
import crypto from 'crypto';

@Injectable()
export class AppService {
  constructor(private readonly prismaService: PrismaService) {}
  async DeleteUser(username: string, password: string): Promise<{ success:true }> {
    const hashedPassword = crypto
      .createHash('sha256')
      .update(password)
      .digest('hex');

    const user = await this.prismaService.user.findFirst({
      where: {
        username: username,
        password: hashedPassword,
      },
    });

    if (!user) {
      throw new NotFoundException('Kullanıcı bulunamadı');
    }

    if (user.is_deleted) {
      throw new BadRequestException('Kullanıcının zaten kaldırma isteği alınmış');
    }

    await this.prismaService.user.update({
      where: {
        id: user.id,
      },
      data: {
        is_deleted: true,
      },
    });

    return {
      success:true
    };
  }
}
