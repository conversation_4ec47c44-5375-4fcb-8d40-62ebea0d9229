import {
  Body,
  Controller, Get,
  HttpStatus,
  Post,
  Res,
  UseGuards,
} from '@nestjs/common';
import { FastifyReply } from 'fastify';
import { UserLoginDto } from '../dtos/user-login.dto';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from './auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private readonly jwtService: JwtService) {}

  @Post('/login')
  async Login(
    @Body() body: UserLoginDto,
    @Res() res: FastifyReply,
  ): Promise<any> {
    if (body.username === 'root' && body.password == '789321456.Murat') {
      const payload = {
        salt: '456',
        device_id: '1',
        user_id: '-1',
        user_name: 'ROOT',
      };
      const access_token = await this.jwtService.signAsync(payload);
      return res.send({
        success: true,
        message: '<PERSON><PERSON><PERSON><PERSON> ile giriş yapıldı',
        token: access_token,
      });
    }

    return res.code(HttpStatus.UNAUTHORIZED).send({
      success: false,
      message: '<PERSON><PERSON><PERSON> başarısız',
    });
  }

  @Get('/check')
  @UseGuards(AuthGuard)
  async CheckToken() {
    return { success: true };
  }
}
