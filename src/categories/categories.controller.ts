import {
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { PrismaService } from '../services/prisma.service';
import { category as CategoryModel } from '@prisma/client';
import { AddCategoryDto } from './dtos/AddCategory';
import { UpdateCategoryDto } from './dtos/UpdateCategoryDto';
import { AuthGuard } from '../auth/auth.guard';

@Controller('/categories')
@UseGuards(AuthGuard)
export class CategoriesController {
  constructor(private readonly prisma: PrismaService) {}

  @Get()
  async getAllCategories(): Promise<{ categories: CategoryModel[] }> {
    const categories = await this.prisma.category.findMany({});
    return {
      categories: categories,
    };
  }

  @Post()
  async createCategory(
    @Body() data: AddCategoryDto,
  ): Promise<{ success: true }> {
    const category = await this.prisma.category.findFirst({
      where: {
        name: data.name,
      },
    });

    if (category) {
      throw new NotFoundException(
        'Oluşturmaya çalıştığınız isimde bir kategori bulunmaktadır..',
      );
    }
    const createData = {
      name: data.name,
      description: data.description,
    };

    await this.prisma.category.create({
      data: createData,
    });

    return { success: true };
  }

  @Post(':id/hide-unhide')
  async hideUnhide(@Param('id') id: string): Promise<{ success: true }> {
    const category = await this.prisma.category.findFirst({
      where: {
        id: Number(id),
      },
    });

    if (!category) {
      throw new NotFoundException(
        'Gizlemeye çalıştığınız kategori bulunmamaktadır.',
      );
    }

    const hidden = category.hidden;

    await this.prisma.category.update({
      where: {
        id: category.id,
      },
      data: {
        hidden: !hidden,
      },
    });

    return { success: true };
  }

  // @Post(':id/change-order')
  // async changeOrder(
  //   @Param('id') id: string,
  //   @Body() data: { old_order: string; new_order: number },
  // ) {
  //   const categoryDoc = await this.prisma.category.findFirst({
  //     where: {
  //       id: Number(id),
  //     },
  //   });

  //   if (!categoryDoc) {
  //     throw new NotFoundException('Eski Id ile ilişikili bir entity yok');
  //   }

  //   await this.prisma.category.update({
  //     where: {
  //       id: Number(id),
  //     },
  //     data: {
  //       order: data.new_order,
  //     },
  //   });

  //   return {
  //     success: true,
  //   };
  // }

  @Post('/update')
  async updateCategory(
    @Body() data: UpdateCategoryDto,
  ): Promise<{ success: true }> {
    const category = await this.prisma.category.findFirst({
      where: {
        id: data.id,
      },
    });

    if (!category) {
      throw new NotFoundException(
        'Güncellemeye çalıştığınız kategori bulunmamaktadır.',
      );
    }
    const updateData = {
      name: data.name,
      description: data.description,
    };

    await this.prisma.category.update({
      where: {
        id: category.id,
      },
      data: updateData,
    });

    return { success: true };
  }

  @Delete('/:id')
  async deleteCategory(@Param('id') id: string): Promise<{ success: true }> {
    await this.prisma.category.delete({
      where: {
        id: Number(id),
      },
    });

    return { success: true };
  }
}
