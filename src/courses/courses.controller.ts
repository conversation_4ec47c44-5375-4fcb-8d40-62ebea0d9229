import {
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { PrismaService } from '../services/prisma.service';
import { course as CourseModel } from '@prisma/client';
import { AddCourseDto } from './dtos/AddCourseDto';
import { UpdateCourseDto } from './dtos/UpdateCourseDto';
import { AuthGuard } from '../auth/auth.guard';

@Controller('/courses')
@UseGuards(AuthGuard)
export class CoursesController {
  constructor(private readonly prisma: PrismaService) {}

  @Get()
  async getAllCourses(): Promise<{ courses: CourseModel[] }> {
    const courses = await this.prisma.course.findMany({
      include: {
        category: true,
      },
    });
    return {
      courses: courses,
    };
  }

  @Post('/')
  async createCourse(@Body() data: AddCourseDto): Promise<{ success: true }> {
    const category = await this.prisma.course.findFirst({
      where: {
        name: data.name,
      },
    });

    if (category) {
      throw new NotFoundException(
        'Oluşturmaya çalıştığınız isimde bir kurs bulunmaktadır..',
      );
    }

    const createData = {
      name: data.name,
      description: data.description,
      category_id: data.category_id,
    };

    await this.prisma.course.create({
      data: createData,
    });

    return { success: true };
  }

  @Post(':id/hide-unhide')
  async hideUnhide(@Param('id') id: string): Promise<{ success: true }> {
    const category = await this.prisma.course.findFirst({
      where: {
        id: Number(id),
      },
    });

    if (!category) {
      throw new NotFoundException(
        'Gizlemeye çalıştığınız kurs bulunmamaktadır.',
      );
    }

    const hidden = category.hidden;

    await this.prisma.course.update({
      where: {
        id: category.id,
      },
      data: {
        hidden: !hidden,
      },
    });

    return { success: true };
  }

  @Post('/update')
  async updateCourse(
    @Body() data: UpdateCourseDto,
  ): Promise<{ success: true }> {
    const course = await this.prisma.course.findFirst({
      where: {
        id: data.course_id,
      },
    });

    if (!course) {
      throw new NotFoundException(
        'Güncellemeye çalıştığınız kurs bulunmamaktadır.',
      );
    }
    const updateData = {
      name: data.name,
      description: data.description,
      category_id: data.category_id,
    };

    await this.prisma.course.update({
      where: {
        id: course.id,
      },
      data: updateData,
    });

    return { success: true };
  }

  @Delete('/:id')
  async deleteCourse(@Param('id') id: string): Promise<{ success: true }> {
    await this.prisma.course.delete({
      where: {
        id: Number(id),
      },
    });

    return { success: true };
  }
}
