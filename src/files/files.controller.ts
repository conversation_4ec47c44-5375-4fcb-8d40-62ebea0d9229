import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Post,
  Res,
  Headers,
  Req,
} from '@nestjs/common';
import { PrismaService } from '../services/prisma.service';
// import { level as LevelModel } from '@prisma/client';
import { file as FileModel } from '@prisma/client';
import { file_provider } from '@prisma/client';
import { AddFileDto } from './dtos/AddFileDto';
import { GoogleService } from '../services/google.service';
import dayjs from 'dayjs';
import busboy from 'busboy';
import * as path from 'path';
import * as fs from 'fs';

import fastify = require('fastify');

@Controller('/files')
export class FilesController {
  constructor(
    private readonly prisma: PrismaService,
    private readonly googleService: GoogleService,
  ) {}

  @Get('/')
  async getFiles(): Promise<{ files: FileModel[] }> {
    const files = await this.prisma.file.findMany();

    return {
      files: files,
    };
  }

  @Get('/:fileId')
  async getLevelFile(@Param('fileId') fileId: string, @Res() response) {
    const file = await this.prisma.file.findFirst({
      where: {
        id: Number(fileId),
      },
    });

    if (!file) {
      throw new BadRequestException('Dosya yok.');
    }

    const googleFile = await this.googleService.getFile(file.path);

    const url = await googleFile.getSignedUrl({
      expires: dayjs().add(10, 'second').toDate(),
      action: 'read',
    });

    await googleFile.getMetadata();

    // return response.status(306).redirect(url[0]);

    if (googleFile.metadata.contentType == 'video/mp4') {
      const htmlString = `
        <!DOCTYPE html>
        <html>
            <body>
            <video width="800" height="600" controls>
              <source src="${url[0]}" type="video/mp4">
            Your browser does not support the video tag.
            </video>
            </body>
        </html>
    `;

      response.header('Content-Type', 'text/html');
      return response.send(htmlString);
    }

    return response.status(306).redirect(url[0]);
  }

  @Post('/')
  async createFile(
    @Body() data: AddFileDto,
  ): Promise<{ success: true; file: FileModel }> {
    const file = await this.prisma.file.findFirst({
      where: {
        path: data.file_path,
        level_id: data.file_level_id,
      },
    });

    if (file) {
      throw new BadRequestException('Bu Levelde bu dosya zaten mevcut.');
    }
    const createData: any = {
      name: data.file_name,
      extension: data.file_ext,
      path: data.file_path,
      filesize: data.file_size,
      provider: file_provider.GOOGLE,
      level_id: data.file_level_id,
    };

    if (data.file_cover) {
      createData.cover = data.file_cover;
    }

    const createdFile = await this.prisma.file.create({
      data: createData,
    });

    return { success: true, file: createdFile };
  }

  @Post('/upload-cover')
  async uploadCover(
    @Headers() headers,
    @Req() req: fastify.FastifyRequest,
    @Res() res: fastify.FastifyReply<any>,
  ) {
    const bb = busboy({ headers: headers });
    let fileName = null;

    bb.on('file', (name, file, info) => {
      const { filename, encoding, mimeType } = info;
      fileName = filename;
      const saveTo = path.join(
        process.env.STATIC_FOLDER_PATH,
        'public',
        'cover',
        `${filename}`,
      );
      file.pipe(fs.createWriteStream(saveTo));
      // console.log(
      //   `File [${name}]: filename: %j, encoding: %j, mimeType: %j`,
      //   filename,
      //   encoding,
      //   mimeType,
      // );
      // file
      //   .on('data', (data) => {
      //     console.log(`File [${name}] got ${data.length} bytes`);
      //   })
      //   .on('close', () => {
      //     console.log(`File [${name}] done`);
      //   });
    });

    bb.on('close', () => {
      // res.raw.writeHead(200, { Connection: 'close' });
      // res.raw.end(`That's all folks!`);

      return res.send({
        url: `${process.env.URL_PATH}/cover/${fileName}`,
        success: true,
      });
    });
    req.raw.pipe(bb);
  }

  @Delete('/:id')
  async deleteFile(@Param('id') id: string) {
    const file = await this.prisma.file.findFirst({
      where: {
        id: Number(id),
      },
    });

    if (!file) {
      throw new NotFoundException(
        'Silmeye çalıştığınız isimde bir dosya bulunmamaktadır.',
      );
    }

    await this.prisma.file.delete({
      where: {
        id: Number(id),
      },
    });

    return { success: true };
  }
}
