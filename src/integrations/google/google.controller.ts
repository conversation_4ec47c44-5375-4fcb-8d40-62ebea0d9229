import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Injectable,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { GoogleService } from '../../services/google.service';
import { mimeToExtension } from '../../libs/helpers';

import slugify from 'slugify';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('/google')
@UseGuards(AuthGuard)
@Injectable()
export class GoogleController {
  constructor(private readonly googleService: GoogleService) {}

  @Get('/buckets')
  async getBuckets() {
    return this.googleService.listAllBuckets();
  }

  @Post('/get-upload-url')
  async getUploadUrl(@Body() body: { filename: string }) {
    if (!body.filename) {
      throw new BadRequestException('Parametre vermediniz');
    }
    const slugged_name = slugify(body.filename, {
      replacement: '_', // replace spaces with replacement character, defaults to `-`
      remove: undefined, // remove characters that match regex, defaults to `undefined`
      lower: true, // convert to lower case, defaults to `false`
      strict: false, // strip special characters except replacement, defaults to `false`
      locale: 'tr', // language code of the locale to use
      trim: true, // trim leading and trailing replacement chars, defaults to `true`
    });

    const completePath = 'panel_files/' + slugged_name;

    if (await this.googleService.fileExists(completePath)) {
      throw new BadRequestException('Aynı isimde dosya zaten var');
    }
    const url = await this.googleService.generateTempUploadUrl(completePath);

    return {
      url,
    };
  }

  // @Post('/upload-file')
  // async uploadFile(@UploadedFile() file: Express.Multer.File) {
  //   console.log(file);
  // }

  @Post('/files')
  async getFiles(@Body() body: { path: string }) {
    let files;

    if (!body || !body.path || body.path === '/') {
      files = await this.googleService.getAllFilesInBucket(
        process.env.GOOGLE_BUCKET_NAME,
      );
    } else {
      files = await this.googleService.getAllFiles(body.path);
    }

    // return files;
    const mappedFiles = files[0].map((file) => {
      return {
        name: file.metadata.name,
        link: file.metadata.selfLink,
        content_type: file.metadata.contentType,
        extension: mimeToExtension(file.metadata.contentType),
        size: file.metadata.size,
      };
    });

    return {
      success: true,
      files: mappedFiles,
    };
  }

  @Delete('/files')
  async deleteFile(
    @Body() body: { file_name: string },
  ): Promise<{ success: true }> {
    await this.googleService.deleteFile(body.file_name);

    return { success: true };
  }
}
