import {
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { PrismaService } from '../services/prisma.service';
import { file as FileModel, level as LevelModel } from '@prisma/client';
import { AddLevelDto } from './dtos/AddLevelDto';
import { UpdateLevelDto } from './dtos/UpdateLevelDto';

@Controller('/levels')
export class LevelsController {
  constructor(private readonly prisma: PrismaService) {}

  @Get('/')
  async getAllLevels(): Promise<{ levels: LevelModel[] }> {
    const levels = await this.prisma.level.findMany({
      include: {
        course: {
          include: {
            category: true,
          },
        },
      },
    });
    return {
      levels: levels,
    };
  }

  @Post('/')
  async createLevel(@Body() data: AddLevelDto): Promise<{ success: true }> {
    const level = await this.prisma.level.findFirst({
      where: {
        title: data.title,
      },
    });

    if (level) {
      throw new NotFoundException(
        'Oluşturmaya çalıştığınız isimde bir kurs bulunmaktadır..',
      );
    }
    const createData = {
      title: data.title,
      course_id: data.course_id,
    };

    await this.prisma.level.create({
      data: createData,
    });

    return { success: true };
  }

  // @Post(':id/hide-unhide')
  // async hideUnhide(@Param('id') id: string): Promise<{ success: true }> {
  //   const level = await this.prisma.level.findFirst({
  //     where: {
  //       id: Number(id),
  //     },
  //   });
  //
  //   if (!level) {
  //     throw new NotFoundException(
  //       'Gizlemeye çalıştığınız level bulunmamaktadır.',
  //     );
  //   }
  //
  //   const hidden = level.hidden;
  //
  //   await this.prisma.level.update({
  //     where: {
  //       id: level.id,
  //     },
  //     data: {
  //       hidden: !hidden,
  //     },
  //   });
  //
  //   return { success: true };
  // }

  @Get('/available-files')
  async getAvailableFiles(
    @Param('levelId') levelId: string,
  ): Promise<{ files: FileModel[] }> {
    const files = await this.prisma.file.findMany({
      where: {
        level_id: null,
      },
    });

    return {
      files: files,
    };
  }

  @Get('/:levelId/files')
  async getLevelFiles(
    @Param('levelId') levelId: string,
  ): Promise<{ files: FileModel[] }> {
    const files = await this.prisma.file.findMany({
      where: {
        level_id: Number(levelId),
      },
    });

    return {
      files: files,
    };
  }

  // @Post('/:levelId/assign-file') //deprecated
  // async assignFileToLevel(
  //   @Body() data: { file_id: string },
  //   @Param('levelId') levelId: string,
  // ) {
  //   const file = await this.prisma.file.findFirst({
  //     where: {
  //       id: Number(data.file_id),
  //     },
  //   });
  //
  //   if (!file) {
  //     throw new NotFoundException(
  //       'Atamaya çalıştığınız dosya bulunmamaktadır.',
  //     );
  //   }
  //
  //   if (file.level_id != null) {
  //     throw new NotFoundException("Dosya zaten başka bir level'e ait");
  //   }
  //
  //   const level = await this.prisma.level.findFirst({
  //     where: {
  //       id: Number(levelId),
  //     },
  //   });
  //
  //   if (!level) {
  //     throw new NotFoundException(
  //       'Atamaya çalıştığınız level bulunmamaktadır.',
  //     );
  //   }
  //
  //   await this.prisma.file.update({
  //     where: {
  //       id: Number(data.file_id),
  //     },
  //     data: { level_id: Number(levelId) },
  //   });
  //
  //   return { success: true };
  // }

  @Delete('/:levelId/remove-file')
  async removeFileFromLevel(
    @Body() data: { file_id: string },
    @Param('levelId') levelId: string,
  ) {
    const file = await this.prisma.file.findFirst({
      where: {
        id: Number(data.file_id),
      },
    });

    if (!file) {
      throw new NotFoundException(
        "Level'den kaldırmaya çalıştığınız dosya bulunmamaktadır.",
      );
    }

    const level = await this.prisma.level.findFirst({
      where: {
        id: Number(levelId),
      },
    });

    if (!level) {
      throw new NotFoundException('Belirttiğiniz level bulunmamaktadır.');
    }

    // await this.prisma.file.update({
    //   where: {
    //     id: Number(data.file_id),
    //   },
    //   data: { level_id: Number(levelId) },
    // });

    await this.prisma.file.delete({
      where: {
        id: Number(data.file_id),
      },
    });

    return { success: true };
  }

  @Post('/update')
  async updateLevel(@Body() data: UpdateLevelDto): Promise<{ success: true }> {
    const level = await this.prisma.level.findFirst({
      where: {
        id: data.level_id,
      },
    });

    if (!level) {
      throw new NotFoundException(
        'Güncellemeye çalıştığınız kurs bulunmamaktadır.',
      );
    }
    const updateData = {
      title: data.title,
      course_id: data.course_id,
    };

    await this.prisma.level.update({
      where: {
        id: level.id,
      },
      data: updateData,
    });

    return { success: true };
  }

  @Delete('/:id')
  async deleteLevel(@Param('id') id: string): Promise<{ success: true }> {
    await this.prisma.level.delete({
      where: {
        id: Number(id),
      },
    });

    return { success: true };
  }
}
