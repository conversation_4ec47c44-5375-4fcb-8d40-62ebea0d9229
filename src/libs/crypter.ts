import Cryptr from 'cryptr';

export const decrypt = (encryptedPassword) => {

  try {

    const cryptr = new Cryptr(process.env.JWT_SECRET)

    return cryptr.decrypt(encryptedPassword)

  } catch (err) {

    console.error('\n\nDecrypt yapılamadı error:')
    console.log(err)
    throw err
  }

}

export const encrypt = (password) => {

  try {

    const cryptr = new Cryptr(process.env.JWT_SECRET)
    return cryptr.encrypt(password)

  } catch (err) {

    console.error('\n\nEncrypt yapılamadı error:')
    console.log(err)
    throw err

  }

}

/*

Old
export const decrypt = (secretKey, salt, encryptedPassword) => {

  try {

    const cryptr = new Cryptr(secretKey + salt)

    return cryptr.decrypt(encryptedPassword)

  } catch (err) {

    console.error('\n\nDecrypt yapılamadı error:')
    console.log(err)



    throw err

  }

}

export const encrypt = (secret_key, salt, password) => {

  try {

    const cryptr = new Cryptr(secret_key + salt)
    return cryptr.encrypt(password)

  } catch (err) {

    console.error('\n\nEncrypt yapılamadı error:')
    console.log(err)


    throw err

  }

}
*/