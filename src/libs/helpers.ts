const mimeMap = {
  'application/pdf': 'pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    'docx',
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'application/vnd.ms-excel': 'xls',
  'text/plain': 'txt',
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'image/gif': 'gif',
  'image/bmp': 'bmp',
  'audio/mpeg': 'mp3',
  'audio/wav': 'wav',
  'video/mp4': 'mp4',
  'video/quicktime': 'mov',
  'application/zip': 'zip',
  'application/x-rar-compressed': 'rar',
  'application/x-tar': 'tar',
  'application/x-7z-compressed': '7z',
  'application/octet-stream': 'bin',
  'application/x-www-form-urlencoded;charset=UTF-8': 'folder',
  'image/svg+xml': 'svg',
  // Diğer MIME türleri ve uzantılar buraya eklenebilir
};
export const mimeToExtension = (mimeType) => {
  // MIME türünün karşılık geldiği dosya uzantısını alın
  const extension = mimeMap[mimeType];
  return extension || 'unknown'; // Bilinmeyen MIME türleri için "unknown" döndürülür
};
