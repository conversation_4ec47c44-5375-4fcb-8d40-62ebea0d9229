import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Response,
  UseGuards,
  Request,
} from '@nestjs/common';
import { default as axios } from 'axios';
import generateHash from 'src/libs/generateHash';
import { PaymentService } from './payment.service';
import { TPayment, TUserCardInfo, TUserPaymentInfo } from './types/TPayment';
import { PrismaService } from 'src/services/prisma.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { ORDER_STATUS } from 'src/enums/order_enums';

@Controller('payment')
@UseGuards(AuthGuard)
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly prisma: PrismaService,
  ) {}

  @Get('/products')
  async getProducts(@Request() request: any) {
    const products = await this.prisma.o_product.findMany({
      where: {
        deleted_at: null,
      },
    });

    console.dir(products);
    return {
      success: true,
      products: products,
    };
  }

  @Post('/purchase')
  async addCreditCard(@Body() body: any, @Request() request: any) {
    const merchant_oid = 'mb' + generateHash(5);
    const user_ip = process.env.PAYTR_SERVER_IP;
    const user_address =
      'Çukurambar, Kızılırmak Mah. Ufuk Üniversitesi Cad. Next Level Loft Office No:4 Kat:20 Ofis 54, Çankaya / Ankara'; // Müşterinizin sitenizde kayıtlı veya form aracılığıyla aldığınız adres bilgisi.

    // Başarılı ödeme sonrası müşterinizin yönlendirileceği sayfa
    // Bu sayfa siparişi onaylayacağınız sayfa değildir! Yalnızca müşterinizi bilgilendireceğiniz sayfadır!

    // find user
    const user = await this.prisma.user.findFirst({
      where: {
        id: Number(request.user.user_id),
      },
      include: {
        credential: true,
      },
    });

    if (!user) {
      throw new BadRequestException('Kullanıcı bulunamadı');
    }

    // find product
    const product = await this.prisma.o_product.findFirst({
      where: {
        id: Number(body.product_id),
      },
    });

    if (!product) {
      throw new BadRequestException(
        'Satın almaya çalıştığınız ürün bulunamadı',
      );
    }

    const paymentData: TPayment = {
      merchant_oid,
      payment_amount: product.price.toString(),
      currency: product.currency_code,
      non_3d: '1',
    };

    const user_basket = JSON.stringify([[product.name, product.price, 1]]);

    const userPaymentInfo: TUserPaymentInfo = {
      user_email: user.email,
      user_ip: user_ip,
      user_name: `${user.credential.name} ${user.credential.surname}`,
      user_address,
      user_phone: user.credential.phone,
      user_basket,
    };

    const userCardInfo: TUserCardInfo = {
      cc_owner: body.cc_owner,
      card_number: body.card_number,
      expiry_month: body.expiry_month,
      expiry_year: body.expiry_year,
      cvv: body.cvv,
    };

    const orderData = {
      user_id: user.id,
      product_id: product.id,
      price: product.price,
      currency_code: product.currency_code,
      status: ORDER_STATUS.CREATED,
      code: merchant_oid,
      created_at: new Date(),
    };

    await this.prisma.o_order.create({
      data: orderData,
    });

    if (body.ctoken_hashed) {
      if (!user.utoken) {
        throw new BadRequestException('Kullanıcı daha önce alışveriş yapmamış');
      }
      await this.paymentService.payWithSavedCard(
        paymentData,
        userPaymentInfo,
        body.ctoken_hashed,
        user.utoken,
      );

      return {
        success: true,
      };
    }

    if (body.save_card) {
      await this.paymentService.payAndSaveCard(
        paymentData,
        userPaymentInfo,
        userCardInfo,
        user.utoken,
      );
      return {
        success: true,
      };
    }

    await this.paymentService.pay(paymentData, userPaymentInfo, userCardInfo);
    return {
      success: true,
    };
  }

  @Get('/card-list')
  async getCardList(@Request() request: any) {
    // return {
    //   success: true,
    //   card_list: [
    //     {
    //       ctoken:
    //         '090af0dd772a75d0db90216831ff16b23cfebc181b818fb121d5263397701714',
    //       last_4: '4029',
    //       month: '06',
    //       year: '30',
    //       c_bank: 'Garanti Bankası',
    //       require_cvv: '0',
    //       c_name: 'Hayrettin GÖK',
    //       c_brand: 'bonus',
    //       c_type: 'credit',
    //       businessCard: 'n',
    //       initial: '5',
    //       schema: 'MASTERCARD',
    //       bank_id: '0062',
    //     },
    //     {
    //       ctoken:
    //         '090af0dd772a75d0db90216831ff16b23cfebc181b818fb121d5263397701714',
    //       last_4: '4029',
    //       month: '06',
    //       year: '30',
    //       c_bank: 'Garanti Bankası',
    //       require_cvv: '0',
    //       c_name: 'Hayrettin GÖK',
    //       c_brand: 'bonus',
    //       c_type: 'credit',
    //       businessCard: 'n',
    //       initial: '5',
    //       schema: 'MASTERCARD',
    //       bank_id: '0062',
    //     },
    //   ],
    // };

    const user = await this.prisma.user.findFirst({
      where: {
        id: Number(request.user.user_id),
      },
    });

    if (!user) {
      throw new BadRequestException('Kullanıcı bulunamadı');
    }

    if (user.utoken == null) {
      return {
        success: true,
        card_list: [],
      };
    }
    const cardList = await this.paymentService.getCardList(user.utoken);

    return {
      success: true,
      card_list: cardList,
    };
  }

  // @Post('/webhook')
  // async webhook(@Body() body: any) {
  //   console.log(body);

  //   axios.request({
  //     url: 'https://discord.com/api/webhooks/1231328971094757418/N7Pu2gKabJceKLJMbkpko7Jt3Xhgob3l3HRE-4n2pfW9eUpk2T31S-6UwnYvk3LAHJ0h',
  //     method: 'POST',
  //     data: {
  //       content: JSON.stringify(body),
  //     },
  //   });

  //   this.paymentService.verifyHashmac({
  //     hash: body.hash,
  //     merchant_oid: body.merchant_oid,
  //     status: body.status,
  //     total_amount: body.total_amount,
  //   });

  //   const order = await this.prisma.o_order.findFirst({
  //     where: {
  //       code: body.merchant_oid,
  //     },
  //   });

  //   if (!order) {
  //     throw new BadRequestException('Sipariş bulunamadı');
  //   }

  //   if (body.status == 'success') {
  //     await this.prisma.o_order.update({
  //       where: {
  //         id: order.id,
  //       },
  //       data: {
  //         status: ORDER_STATUS.COMPLETED,
  //         success_date: new Date(),
  //       },
  //     });

  //     if (body.utoken) {
  //       await this.prisma.user.update({
  //         where: {
  //           id: order.user_id,
  //         },
  //         data: {
  //           utoken: body.utoken,
  //         },
  //       });
  //     }
  //   } else {
  //     await this.prisma.o_order.update({
  //       where: {
  //         id: order.id,
  //       },
  //       data: {
  //         status: ORDER_STATUS.CANCELLED,
  //         error_code: body.failed_reason_code,
  //         error_reason: body.failed_reason_msg,
  //       },
  //     });
  //   }

  //   return {
  //     succes: true,
  //   };
  // }
}
