import { Injectable } from '@nestjs/common';
import { response } from 'express';
import { default as axios } from 'axios';
import generateHash from 'src/libs/generateHash';
import crypto from 'crypto';
import { decrypt, encrypt } from 'src/libs/crypter';
import {
  THashData,
  TPayment,
  TUserCardInfo,
  TUserPaymentInfo,
} from './types/TPayment';

const merchant_id = process.env.PAYTR_MERCHANT_ID;
const merchant_key = process.env.PAYTR_MERCHANT_KEY;
const merchant_salt = process.env.PAYTR_MERCHANT_SALT;

type TBasketEntry = [string, string, number];

@Injectable()
export class PaymentService {
  async __createHash(hashData: THashData) {
    const hashSTR = `${hashData.merchant_id}${hashData.user_ip}${hashData.merchant_oid}${hashData.email}${hashData.payment_amount}${hashData.payment_type}${hashData.installment_count}${hashData.currency}${hashData.test_mode}${hashData.non_3d}`;
    const paytr_token = hashSTR + merchant_salt;
    return crypto
      .createHmac('sha256', merchant_key)
      .update(paytr_token)
      .digest('base64');
  }

  async payAndSaveCard(
    paymentData: TPayment,
    userPaymentInfo: TUserPaymentInfo,
    userCardInfo: TUserCardInfo,
    utoken?: string,
  ) {
    return this.pay(
      paymentData,
      userPaymentInfo,
      userCardInfo,
      utoken,
      null,
      '1',
    );
  }

  async payWithSavedCard(
    paymentData: TPayment,
    userPaymentInfo: TUserPaymentInfo,
    ctoken: string,
    utoken: string,
  ) {
    return this.pay(
      paymentData,
      userPaymentInfo,
      null,
      utoken,
      decrypt(ctoken),
    );
  }

  async pay(
    paymentData: TPayment,
    userPaymentInfo: TUserPaymentInfo,
    userCardInfo?: TUserCardInfo,
    utoken?: string, //Daha önce kayıtlı ise bu veri alınacak.
    ctoken?: string,
    storeCard = '0',
  ) {
    // const non3d_test_failed = '0'; // Non3d Test Failed.
    const test_mode: any = process.env.PAYTR_TEST_MODE; // Mağaza canlı modda iken test işlem yapmak için 1 olarak gönderilebilir.

    const token = await this.__createHash({
      merchant_id,
      merchant_oid: paymentData.merchant_oid,
      user_ip: userPaymentInfo.user_ip,
      email: userPaymentInfo.user_email,
      payment_amount: paymentData.payment_amount,
      payment_type: 'card',
      installment_count: '0',
      currency: 'TL',
      test_mode,
      non_3d: '1',
    });

    const context: any = {
      test_mode,
      merchant_id,
      merchant_oid: paymentData.merchant_oid,
      //user infos
      user_ip: userPaymentInfo.user_ip,
      email: userPaymentInfo.user_email,
      user_name: userPaymentInfo.user_name,
      user_address: userPaymentInfo.user_address,
      user_phone: userPaymentInfo.user_phone,
      user_basket: userPaymentInfo.user_basket,
      //-
      client_lang: 'tr',
      payment_amount: paymentData.payment_amount,
      payment_type: 'card',
      currency: paymentData.currency || 'TL',
      installment_count: '0',
      non_3d: '1',
      merchant_ok_url: process.env.PAYTR_WEBHOOK_URL + '/payment/success',
      merchant_fail_url: process.env.PAYTR_WEBHOOK_URL + '/payment/fail',
      debug_on: '1',
      paytr_token: token,
      store_card: storeCard,
    };

    if (utoken) {
      context.utoken = utoken;
    }

    // Kayıtlı kartla satın alacaksa
    if (ctoken) {
      context.utoken = utoken;
      context.ctoken = ctoken;
    } else {
      Object.assign(context, {
        cc_owner: userCardInfo.cc_owner,
        card_number: userCardInfo.card_number,
        expiry_month: userCardInfo.expiry_month,
        expiry_year: userCardInfo.expiry_year,
        cvv: userCardInfo.cvv,
      });
    }
    if (test_mode == '1' && !ctoken) {
      Object.assign(context, {
        card_number: '9792030394440796',
        expiry_month: '08',
        expiry_year: '24',
        cvv: '000',
      });
    }

    // if (test_mode == '0' && !ctoken) {
    //   Object.assign(context, {
    //     card_number: '****************',
    //     expiry_month: '08',
    //     expiry_year: '24',
    //     cvv: '444',
    //   });
    // }

    return axios
      .request({
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        url: 'https://www.paytr.com/odeme',
        method: 'POST',
        data: context,
      })
      .then((res) => {
        console.log(res.data);
      })
      .catch((err) => {
        console.log(err);
      });
  }

  async getCardList(utoken) {
    // const merchant_id = process.env.PAYTR_MERCHANT_ID;
    // const merchant_key = process.env.PAYTR_MERCHANT_KEY;
    // const merchant_salt = process.env.PAYTR_MERCHANT_SALT;

    // const utoken = '63d2e34d03984d81aa54d6e82ecab657501f46c2115706bf0c4dee3600795806';

    const paytr_token = crypto
      .createHmac('sha256', merchant_key)
      .update(utoken + merchant_salt)
      .digest('base64');

    return axios
      .request({
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        url: 'https://www.paytr.com/odeme/capi/list',
        method: 'POST',
        data: {
          merchant_id,
          utoken,
          paytr_token,
        },
      })
      .then((res) => {
        console.log(res.data);
        return res.data.map((cardData) => {
          const { last_4, month, year, c_bank, c_name, schema } = cardData;

          return {
            ctoken_hashed: encrypt(cardData.ctoken),
            last_4,
            month,
            year,
            c_bank,
            c_name,
            schema,
          };
        });
      });
  }

  async verifyHashmac(data: TVerifyWebhook) {
    const paytr_token =
      data.merchant_oid + merchant_salt + data.status + data.total_amount;
    const token = crypto
      .createHmac('sha256', merchant_key)
      .update(paytr_token)
      .digest('base64');

    // Oluşturulan hash'i, paytr'dan gelen post içindeki hash ile karşılaştır (isteğin paytr'dan geldiğine ve değişmediğine emin olmak için)
    // Bu işlemi yapmazsanız maddi zarara uğramanız olasıdır.

    if (token != data.hash) {
      throw new Error('PAYTR notification failed: bad hash');
    }
  }
}

type TVerifyWebhook = {
  merchant_oid: string;
  status: string;
  total_amount: string;
  hash: string;
};
