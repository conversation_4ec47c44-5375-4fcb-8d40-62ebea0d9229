export type TPayment = {
  merchant_oid: string;
  payment_amount: string;
  currency?: string;
  non_3d: string;
};

export type TUserCardInfo = {
  cc_owner: string;
  card_number: string;
  expiry_month: string;
  expiry_year: string;
  cvv: string;
};

export type TUserPaymentInfo = {
  user_email: string;
  user_ip: string;
  user_name: string;
  user_address: string;
  user_phone: string;
  user_basket: string; // JSON.Strigify Basket
};

export type THashData = {
  merchant_id: string;
  user_ip: string;
  merchant_oid: string;
  email: string;
  payment_amount: string;
  payment_type: string;
  installment_count: string;
  currency: string;
  test_mode: string;
  non_3d: string;
};
