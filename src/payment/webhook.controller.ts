import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { default as axios } from 'axios';
import { PaymentService } from './payment.service';
import { PrismaService } from 'src/services/prisma.service';
import { ORDER_STATUS } from 'src/enums/order_enums';
import dayjs from 'dayjs';

@Controller('payment')
export class WebhookController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly prisma: PrismaService,
  ) {}

  @Get('/success')
  async successPaymnet(@Param() param: any, @Query() query: any) {
    console.log('/success');

    return { success: true };
  }

  @Get('/fail')
  async failPaymnet(@Param() param: any, @Query() query: any) {
    console.log('/fail');
    console.log(query);

    return { success: false };
  }

  @Post('/webhook')
  async webhook(@Body() body: any) {
    console.log(body);

    try {
      axios.request({
        url: 'https://discord.com/api/webhooks/1231328971094757418/N7Pu2gKabJceKLJMbkpko7Jt3Xhgob3l3HRE-4n2pfW9eUpk2T31S-6UwnYvk3LAHJ0h',
        method: 'POST',
        data: {
          content: JSON.stringify(body),
        },
      });

      this.paymentService.verifyHashmac({
        hash: body.hash,
        merchant_oid: body.merchant_oid,
        status: body.status,
        total_amount: body.total_amount,
      });

      const order = await this.prisma.o_order.findFirst({
        where: {
          code: body.merchant_oid,
        },
      });

      if (!order) {
        throw new BadRequestException('Sipariş bulunamadı');
      }

      if (body.status == 'success') {
        // product update
        const product = await this.prisma.o_product.findFirst({
          where: {
            id: order.product_id,
          },
        });

        if (!product) {
          throw new BadRequestException('Ürün bulunamadı');
        }

        const user = await this.prisma.user.findFirst({
          where: {
            id: order.user_id,
          },
        });

        if (!user) {
          throw new BadRequestException('Kullanıcı bulunamadı');
        }

        const userCourse = await this.prisma.user_course.findFirst({
          orderBy: {
            id: 'desc',
          },
          where: {
            id: product.ext_id,
          },
          take: 1,
        });

        if (!userCourse) {
          throw new BadRequestException('Atanmış kurs bulunamadı');
        }

        const newExpireDate = dayjs(userCourse.expires_at)
          .add(product.days, 'days')
          .toDate();

        await this.prisma.user_course.update({
          where: {
            id: userCourse.id,
          },
          data: {
            expires_at: newExpireDate,
          },
        });
        //---

        await this.prisma.o_order.update({
          where: {
            id: order.id,
          },
          data: {
            status: ORDER_STATUS.COMPLETED,
            success_date: new Date(),
          },
        });

        if (body.utoken) {
          await this.prisma.user.update({
            where: {
              id: order.user_id,
            },
            data: {
              utoken: body.utoken,
            },
          });
        }
      } else {
        await this.prisma.o_order.update({
          where: {
            id: order.id,
          },
          data: {
            status: ORDER_STATUS.CANCELLED,
            error_code: body.failed_reason_code,
            error_reason: body.failed_reason_msg,
          },
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      return 'OK';
    }
  }
}
