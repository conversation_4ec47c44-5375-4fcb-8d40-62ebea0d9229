import { BadRequestException, Injectable } from '@nestjs/common';

import {
  GetBucketsResponse,
  GetFilesResponse,
  Storage,
} from '@google-cloud/storage';
import * as path from 'path';

@Injectable()
export class GoogleService {
  private readonly googleStorage: Storage;

  constructor() {
    // AWS kimlik bilgilerini ve bölgeyi ayarlayın
    this.googleStorage = new Storage({
      keyFilename: path.join(process.cwd(), 'services.json'),
    });
  }

  async listAllBuckets(): Promise<GetBucketsResponse> {
    //unused
    return this.googleStorage.getBuckets();
  }

  async deleteFile(fileName: string) {
    const file = this.googleStorage
      .bucket(process.env.GOOGLE_BUCKET_NAME)
      .file(fileName);

    if (!(await file.exists())) {
      throw new BadRequestException('Silmeye çalıştığınız dosya bulunamadı');
    }
    await file.delete();
  }

  async generateTempUploadUrl(path: string, availableMinutes = 120) {
    await this.googleStorage
      .bucket(process.env.GOOGLE_BUCKET_NAME)
      .setCorsConfiguration([
        {
          maxAgeSeconds: 3600,
          method: ['*'],
          origin: ['*'],
          responseHeader: ['*'],
        },
      ]);

    const [url] = await this.googleStorage
      .bucket(process.env.GOOGLE_BUCKET_NAME)
      .file(path)
      .getSignedUrl({
        version: 'v4',
        action: 'write',
        expires: Date.now() + availableMinutes * 60 * 1000, // 15 minutes
        // contentType: 'application/x-www-form-urlencoded',
      });
    return url;
  }

  async getAllFilesInBucket(bucketName: string): Promise<GetFilesResponse> {
    //unused
    return this.googleStorage.bucket(bucketName).getFiles({
      // prefix: 'advanced_lateks'
    });
  }

  async getAllFiles(path: string) {
    return this.googleStorage.bucket(process.env.GOOGLE_BUCKET_NAME).getFiles({
      prefix: path,
    });
  }

  async getFile(path: string) {
    return this.googleStorage.bucket(process.env.GOOGLE_BUCKET_NAME).file(path);
  }

  async fileExists(path: string) {
    const hasExists = await this.googleStorage
      .bucket(process.env.GOOGLE_BUCKET_NAME)
      .file(path)
      .exists();

    return hasExists[0];
  }
}
