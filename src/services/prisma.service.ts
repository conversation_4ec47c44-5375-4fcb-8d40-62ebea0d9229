import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {
    await this.$connect();

    //soft-delete

    // this.$use(async (params, next) => {
    //   // Check incoming query type
    //   if (params.model == 'user_course') {
    //     if (params.action == 'delete') {
    //       // Delete queries
    //       // Change action to an update
    //       params.action = 'update';
    //       params.args['data'] = { deleted_at: new Date() };
    //     }
    //     if (params.action == 'deleteMany') {
    //       // Delete many queries
    //       params.action = 'updateMany';
    //       if (params.args.data != undefined) {
    //         params.args.data['deleted_at'] = new Date();
    //       } else {
    //         params.args['data'] = { deleted_at: new Date() };
    //       }
    //     }
    //   }
    //   return next(params);
    // });
  }
}
