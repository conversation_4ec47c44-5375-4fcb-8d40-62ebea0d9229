import { Injectable } from '@nestjs/common';
import {
  S3Client,
  ListBucketsCommand,
  ListObjectsCommand,
  ListBucketsCommandOutput,
  ListObjectsCommandOutput,
} from '@aws-sdk/client-s3';

@Injectable()
export class S3Service {
  private readonly s3Client: S3Client;

  constructor() {
    // AWS kimlik bilgilerini ve bölgeyi ayarlayın

    this.s3Client = new S3Client({
      credentials: {
        accessKeyId: process.env.AMAZON_ACCESS_KEY_ID,
        secretAccessKey: process.env.AMAZON_ACCESS_KEY_SECRET,
      },
      region: 'eu-central-1',
    });
  }

  async listAllBuckets(): Promise<{ buckets: ListBucketsCommandOutput }> {
    const commnd: ListBucketsCommand = new ListBucketsCommand({});

    const buckets = await this.s3Client.send(commnd);

    return {
      buckets,
    };

    // const params: AWS.S3.GetObjectRequest = {
    //   Bucket: bucketName,
    //   Key: key,
    //   Expires: 60 * 5, // URL'in 5 dakika süreyle geçerli olmasını sağlar. Zamanı ihtiyacınıza göre ayarlayabilirsiniz.
    // };
    //
    // // getSignedUrl metodunu kullanarak genel URL'yi alın
    // const url = await this.s3.getSignedUrlPromise('getObject', params);
    // return url;
  }

  async listAllFiles(): Promise<{ files: any }> {
    const commnd = new ListObjectsCommand({ Bucket: 'darte-bucket' });

    const files = await this.s3Client.send(commnd);

    return {
      files: files.Contents,
    };
  }
}
