import { z } from 'nestjs-zod/z';
import { createZodDto } from 'nestjs-zod';

const phoneRegex = new RegExp(
  /^([\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/,
);

const EditCredentialSchema = z.object({
  name: z.string(),
  surname: z.string(),
  sex: z.enum(['MALE', 'FEMALE']),
  phone: z
    .string()
    .regex(phoneRegex, 'Lütfen geçerli bir telefon numarası giriniz'),
});

export class EditCredentialDto extends createZodDto(EditCredentialSchema) {}
