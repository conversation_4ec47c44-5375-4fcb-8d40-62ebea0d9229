import { z } from 'nestjs-zod/z';
import { createZodDto } from 'nestjs-zod';

const userTypeZod = z.enum([
  'EDUCATOR',
  'ADMIN',
  'USER',
  'NEW_USER',
  'STUDENT'
]);

const EditUserSchema = z.object({
  email: z.string().email('Lütfen geçerli bir email giriniz'),
  password: z.string().optional(),
  user_type: userTypeZod,
  username: z.string(),
});

export type UserType = z.infer<typeof userTypeZod>;
export class EditUserDto extends createZodDto(EditUserSchema) {}
