import {
  <PERSON>,
  Get,
  Param,
  BadRequestException,
  Post,
  Body,
  UseGuards,
  NotFoundException,
  Delete,
  Put,
} from '@nestjs/common';

import * as crypto from 'crypto';

import { UserService } from './user.service';
import dayjs from 'dayjs';
import {
  user as UserModel,
  user_course as UserCourseModel,
  course as CourseModel,
} from '@prisma/client';
import { PrismaService } from '../services/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '../auth/auth.guard';
import { AssignUserDto } from './dtos/AssignUserDto';
import { EditUserDto } from './dtos/EditUserDto';
import { EditCredentialDto } from './dtos/EditCredentialDto';
import { EditAssignUserDto } from './dtos/EditAssignUserDto';
import { user_statuses } from '../enums/user_enums';

@Controller('users')
@UseGuards(AuthGuard)
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly prisma: PrismaService,
  ) {}

  @Get()
  async getAllUsers(): Promise<{ users: UserModel[] }> {
    const users = await this.userService.users({});

    return {
      users: users,
    };
  }

  @Get('/:id/edit')
  async getUserInfo(@Param('id') id: string): Promise<{ user: UserModel }> {
    if (!id) {
      throw new BadRequestException('id verilmedi');
    }
    const user = await this.userService.user({ id: Number(id) });

    user.password = '';
    return {
      user: user,
    };
  }

  @Post('/:id/edit')
  async editUserInfo(
    @Param('id') id: string,
    @Body() editUserData: EditUserDto,
  ): Promise<{ user: UserModel; message: string }> {
    if (!id) {
      throw new BadRequestException('id verilmedi');
    }
    const user = await this.userService.user({ id: Number(id) });

    if (!user) {
      throw new NotFoundException('Kullanıcı bulunamadı');
    }

    const data: EditUserDto = {
      email: editUserData.email,
      username: editUserData.username,
      user_type: editUserData.user_type,
    };

    // user.email = editUserData.email;
    // user.username = editUserData.username;
    // user.user_type = editUserData.user_type;

    if (editUserData.password && editUserData.password != '') {
      data.password = crypto
        .createHash('sha256')
        .update(editUserData.password)
        .digest('hex');
    }

    await this.prisma.user.update({
      where: {
        id: Number(id),
      },
      data: data,
    });

    return {
      message: 'Kullanıcı bilgileri başarı ile güncelleştirilmiştir.',
      user: user,
    };
  }

  @Post('/:id/credential/edit')
  async editUserCredential(
    @Param('id') id: string,
    @Body() editCredentialData: EditCredentialDto,
  ): Promise<{ message: string }> {
    if (!id) {
      throw new BadRequestException('id verilmedi');
    }
    const user = await this.userService.user({ id: Number(id) });

    if (!user) {
      throw new NotFoundException('Kullanıcı bulunamadı');
    }

    const data: EditCredentialDto = {
      name: editCredentialData.name,
      surname: editCredentialData.surname,
      sex: editCredentialData.sex,
      phone: editCredentialData.phone,
    };

    await this.prisma.credential.update({
      where: {
        id: Number(user.credential_id),
      },
      data: data,
    });

    return {
      message:
        'Kullanıcı Credential bilgileri başarı ile güncelleştirilmiştir.',
    };
  }

  @Post('/:id/active-deactive')
  async activeDeactiveUser(
    @Param('id') id: string,
  ): Promise<{ message: string }> {
    if (!id) {
      throw new BadRequestException('id verilmedi');
    }
    const user = await this.userService.user({ id: Number(id) });

    if (!user) {
      throw new NotFoundException('Kullanıcı bulunamadı');
    }

    const isDeleted = user.is_deleted;

    await this.prisma.user.update({
      where: {
        id: Number(id),
      },
      data: {
        is_deleted: !isDeleted,
      },
    });

    let message = 'Kullanıcı Girişi tekrardan açıldı';
    if (isDeleted) {
      message = 'Kullanıcı girişi kapatıldı';
    }

    return {
      message: message,
    };
  }

  @Post('/:id/lock-unlock')
  async lockUnlock(@Param('id') id: string): Promise<{ message: string }> {
    if (!id) {
      throw new BadRequestException('id verilmedi');
    }
    const user = await this.userService.user({ id: Number(id) });

    if (!user) {
      throw new NotFoundException('Kullanıcı bulunamadı');
    }

    const isLocked = user.status === user_statuses.LOCKED;

    await this.prisma.user.update({
      where: {
        id: Number(id),
      },
      data: {
        status: isLocked ? user_statuses.ONLINE : user_statuses.LOCKED,
      },
    });

    return {
      message: 'Başarı ile işlem gerçekleştirildi.',
    };
  }

  @Get('/:id/user-courses')
  async getUserCourse(@Param('id') id: string): Promise<UserCourseModel[]> {
    return this.prisma.user_course.findMany({
      where: {
        OR: [{ user_id: Number(id) }, { educator_id: Number(id) }],
      },
      include: {
        user: {
          include: {
            credential: true,
          },
        },
        educator: {
          include: {
            credential: true,
          },
        },
        course: {
          include: {
            category: true,
          },
        },
      },
    });
  }

  @Post('/user-courses/:userCourseId/retrieve')
  async retrieveUserCourse(
    @Param('userCourseId') userCourseId: string,
  ): Promise<{ success: true }> {
    const userCourse = await this.prisma.user.findFirst({
      where: {
        id: Number(userCourseId),
      },
    });

    if (!userCourse) {
      throw new NotFoundException('Kurs buluamadı');
    }

    await this.prisma.user_course.update({
      where: { id: Number(userCourseId) },
      data: { deleted_at: null },
    });

    return {
      success: true,
    };
  }

  @Delete('/user-courses/:userCourseId')
  async deleteUserCourse(
    @Param('userCourseId') userCourseId: string,
  ): Promise<{ success: true }> {
    const userCourse = await this.prisma.user.findFirst({
      where: {
        id: Number(userCourseId),
      },
    });

    if (!userCourse) {
      throw new NotFoundException('Kurs buluamadı');
    }

    await this.prisma.user_course.update({
      where: { id: Number(userCourseId) },
      data: { deleted_at: new Date() },
    });

    return {
      success: true,
    };
  }

  @Get('/:id/available-courses')
  async availableUserCourses(
    @Param('id') id: string,
  ): Promise<{ courses: CourseModel[] }> {
    const user_courses = await this.prisma.user_course.findMany({
      where: {
        user_id: Number(id),
      },
      include: {
        user: {
          include: {
            credential: true,
          },
        },
        educator: {
          include: {
            credential: true,
          },
        },
        course: {
          include: {
            category: true,
          },
        },
      },
    });

    const ids = user_courses.map((userCourse) => userCourse.course.id).flat();

    const course = await this.prisma.course.findMany({
      where: {
        id: {
          notIn: ids,
        },
      },
      include: {
        category: true,
      },
    });

    return { courses: course };
  }

  @Get('/available-educators')
  async getAvailableEducators(): Promise<{ educators: UserModel[] }> {
    const educators = await this.prisma.user.findMany({
      where: {
        user_type: {
          in: ['ADMIN', 'EDUCATOR'],
        },
      },
      include: {
        credential: true,
      },
    });

    return { educators: educators };
  }

  @Post('/assign-user-courses')
  async assignUserCourse(@Body() body: AssignUserDto) {
    const course = await this.prisma.course.findUnique({
      where: {
        id: body.course_id,
      },
    });

    if (!course) {
      throw new NotFoundException({
        success: false,
        message: 'Kurs bulunamadı',
      });
    }

    const educator = await this.prisma.user.findFirst({
      where: {
        id: body.educator_id,
        user_type: {
          in: ['ADMIN', 'EDUCATOR'],
        },
      },
    });

    if (!educator) {
      throw new NotFoundException({
        success: false,
        message: 'Eğitmen  bulunamadı',
      });
    }

    const user = await this.prisma.user.findFirst({
      where: {
        id: body.user_id,
      },
    });

    if (!user) {
      throw new NotFoundException({
        success: false,
        message: 'Kursiyer bulunamadı',
      });
    }

    if (user.user_type === 'NEW_USER') {
      throw new BadRequestException({
        success: false,
        message:
          'Kursiyeriniz henüz profilini tamamlamamış. Önce Profilini tamamlamasını bekleyiniz.',
      });
    }

    const expirationDate = dayjs().add(1, 'year');

    const userCourse = await this.prisma.user_course.create({
      data: {
        user_id: body.user_id,
        educator_id: body.educator_id,
        course_id: body.course_id,
        level: 1,
        created_at: dayjs().toDate(),
        updated_at: dayjs().toDate(),
        expires_at: expirationDate.toDate(),
      },
    });

    return {
      success: true,
      message: 'Başarı ile atama gerçekleştirildi',
      user_course: userCourse,
    };

    //new UserCourse
    // user_id,course_id,educator_id,level
    // date & expires_at
  }

  @Post('/update-user-course')
  async editUserCourse(@Body() body: EditAssignUserDto) {
    const course = await this.prisma.course.findUnique({
      where: {
        id: body.course_id,
      },
    });

    if (!course) {
      throw new NotFoundException({
        success: false,
        message: 'Kurs bulunamadı',
      });
    }

    const educator = await this.prisma.user.findFirst({
      where: {
        id: body.educator_id,
        user_type: {
          in: ['ADMIN', 'EDUCATOR'],
        },
      },
    });

    if (!educator) {
      throw new NotFoundException({
        success: false,
        message: 'Eğitmen  bulunamadı',
      });
    }

    const user = await this.prisma.user.findFirst({
      where: {
        id: body.user_id,
      },
    });

    if (!user) {
      throw new NotFoundException({
        success: false,
        message: 'Kursiyer bulunamadı',
      });
    }

    if (user.user_type === 'NEW_USER') {
      throw new BadRequestException({
        success: false,
        message:
          'Kursiyeriniz henüz profilini tamamlamamış. Önce Profilini tamamlamasını bekleyiniz.',
      });
    }

    const lastDate = dayjs.unix(body.expires_at).toDate();

    const userCourse = await this.prisma.user_course.update({
      where: {
        id: Number(body.user_course_id),
      },
      data: {
        user_id: body.user_id,
        educator_id: body.educator_id,
        course_id: body.course_id,
        level: body.level,
        expires_at: lastDate,
      },
    });

    return {
      success: true,
      message: 'Başarı ile atama gerçekleştirildi',
      user_course: userCourse,
    };

    //new UserCourse
    // user_id,course_id,educator_id,level
    // date & expires_at
  }
}
