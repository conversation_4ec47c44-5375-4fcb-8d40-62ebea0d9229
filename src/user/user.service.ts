import { Injectable } from '@nestjs/common';
import { PrismaService } from '../services/prisma.service';
import { user, Prisma } from '@prisma/client';

@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}

  async user(
    userWhereUniqueInput: Prisma.userWhereUniqueInput,
  ): Promise<user | null> {
    return this.prisma.user.findUnique({
      include: {
        credential: true,
      },
      where: userWhereUniqueInput,
    });
  }

  // async updateUser(
  //   userWhereUniqueInput: Prisma.userWhereUniqueInput,
  //   data:Partial<user>
  //   ){
  //
  //   this.prisma.user.update(
  //     where:{
  //
  //   }
  //   )
  //
  // }

  async users(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.userWhereUniqueInput;
    where?: Prisma.userWhereInput;
    orderBy?: Prisma.userOrderByWithRelationInput;
  }): Promise<user[]> {
    const { skip, take, cursor, where, orderBy } = params;
    return this.prisma.user.findMany({
      include: {
        credential: true,
      },
      skip,
      take,
      cursor,
      where,
      orderBy,
    });
  }

  //   async createUser(data: Prisma.UserCreateInput): Promise<User> {
  //     return this.prisma.user.create({
  //       data,
  //     });
  //   }

  async listUsers(): Promise<user[]> {
    // const { where, data } = params;
    return this.prisma.user.findMany();
  }
}
